; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define MyAppName "Wegweiser Agent"
#define MyAppVersion "1.0"
#define MyAppPublisher "Wegweiser by Old Forge Technologies"
#define MyAppURL "https://www.wegweiser.tech/"
#define MyAppExeName "agent.exe"
#define AppRootPath "Wegweiser"

[Parameters]
; Define a parameter for groupuuid
; Name: "groupuuid"; Type: string; Description: "Specify the group UUID"; Default: ""
Name: "groupuuid"; Description: "Specify the group UUID"; Default: ""
Name: "serverAddr"; Description: "Specify the server address"; Default: "app.wegweiser.tech"

[Setup]
; NOTE: The value of AppId uniquely identifies this application.
AppId={{3d77c6a8-6cb9-4df3-81ec-023c9ec29068}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
DefaultDirName={autopf}\{#AppRootPath}
DisableDirPage=no
DefaultGroupName={#MyAppName}
AllowNoIcons=yes
OutputDir=C:\Users\<USER>\Desktop\Wegweiser\Agent007
OutputBaseFilename=wegweiserAgentInstaller
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; Visual elements
SetupIconFile=assets\wegweiser.ico
UninstallDisplayIcon={app}\assets\wegweiser.ico
WizardImageFile=assets\wizard-large.bmp
WizardSmallImageFile=assets\wizard-small.bmp

; Version info
VersionInfoCompany={#MyAppPublisher}
VersionInfoCopyright=Copyright (C) 2024 Old Forge Technologies
VersionInfoDescription=Wegweiser Agent Installation
VersionInfoProductName={#MyAppName}
VersionInfoProductVersion={#MyAppVersion}

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
Name: "german"; MessagesFile: "compiler:Languages\German.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Dirs]
Name: "{app}\Agent"
Name: "{app}\Config"
Name: "{app}\Files"
Name: "{app}\Files\failed_uploads"
Name: "{app}\Files\Loki"
Name: "{app}\Logs"
Name: "{app}\Scripts"
Name: "{app}\Snippets"
Name: "{app}\assets"

[Files]
; Python environment with all dependencies
Source: "python-weg\*"; DestDir: "{app}\Agent\python-weg"; Flags: ignoreversion recursesubdirs createallsubdirs

; OSQuery installer
Source: "vendors\osquery.msi"; DestDir: "{tmp}"; Flags: deleteafterinstall

; Main agent script
Source: "agent\agent.py"; DestDir: "{app}\Scripts"; Flags: ignoreversion


; PowerShell scripts
Source: "scripts\createWinTaskAgent.ps1"; DestDir: "{app}\Scripts"; Flags: ignoreversion
Source: "scripts\registerAgent.ps1"; DestDir: "{app}\Scripts"; Flags: ignoreversion
Source: "scripts\msinfo32-evaluatorAgent.ps1"; DestDir: "{app}\Scripts"; Flags: ignoreversion

; Visual assets
Source: "assets\wegweiser.ico"; DestDir: "{app}\assets"; Flags: ignoreversion

[Icons]
;Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
;Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
; FIXED: Directly pass quoted parameters to ensure they're properly handled
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\Scripts\registerAgent.ps1"" -GroupUUID ""{param:groupuuid}"" -serverAddr ""{param:serverAddr}"""; WorkingDir: "{app}"; Flags: runhidden
Filename: "powershell.exe"; Parameters: "-ExecutionPolicy Bypass -File ""{app}\Scripts\createWinTaskAgent.ps1"""; WorkingDir: "{app}"; Flags: runhidden

; Install OSQuery silently
Filename: "msiexec.exe"; Parameters: "/i ""{tmp}\osquery.msi"" /qn"; Flags: runhidden waituntilterminated; StatusMsg: "Installing osquery..."

[Code]
function GetValidatedGroupUUID(Value: string): string;
var
    GroupUUID: string;
begin
    GroupUUID := ExpandConstant('{param:groupuuid}');
    if (Length(GroupUUID) <> 36) then
        begin
            MsgBox('Group UUID: ' + GroupUUID + ' must be exactly 36 characters long.', mbError, MB_OK);
            Result := ''
        end
    else
        begin
            Result := GroupUUID;
        end;
end;

function getServerAddr(Value: string): string;
var
    serverAddr: string;
begin
    serverAddr := ExpandConstant('{param:serverAddr}');
    Result := serverAddr;
    if serverAddr = '' then
        begin
            MsgBox('Error Server Address is required: ', mbError, MB_OK);
            Result := ''; 
        end
end;

function InitializeSetup(): Boolean;
var
    groupuuid: string;
    serveraddr: string;
begin
    Result := True;
    groupuuid := GetValidatedGroupUUID('');
    if groupuuid = '' then
        begin
            Result := False;
        end;
    serveraddr := getServerAddr('');
    if serveraddr = '' then
        begin
            Result := False;
        end;
end;