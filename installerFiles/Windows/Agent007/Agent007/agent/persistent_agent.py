# Filepath: persistent_agent.py

import os
import sys
import json
import asyncio
import websockets
import logging
import platform
import socket
import time
import requests
from datetime import datetime
from logzero import logger, logfile
from dotenv import load_dotenv
import uuid

# Ensure proper directory paths - improved path detection
if hasattr(sys, 'frozen'):
    # Running as compiled executable
    application_path = os.path.dirname(sys.executable)
else:
    # Running as script
    application_path = os.path.dirname(os.path.abspath(__file__))

# Check if we're in Scripts directory and adjust paths accordingly
base_dir = None
if os.path.basename(os.path.dirname(application_path)).lower() == 'scripts':
    # We're in Scripts folder, go up one level
    base_dir = os.path.dirname(os.path.dirname(application_path))
else:
    # Assume we're directly in the main folder
    base_dir = os.path.dirname(application_path)

# Try Program Files path as fallback if base_dir doesn't have expected structure
if not os.path.exists(os.path.join(base_dir, 'Config')):
    potential_paths = [
        r'C:\Program Files (x86)\Wegweiser',
        r'C:\Program Files\Wegweiser',
        r'C:\Wegweiser'
    ]
    for path in potential_paths:
        if os.path.exists(os.path.join(path, 'Config')):
            base_dir = path
            break

# Setup derived paths
log_dir = os.path.join(base_dir, 'Logs')
config_dir = os.path.join(base_dir, 'Config')

# Set working directory to script location
os.chdir(application_path)

# Ensure log directory exists
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

# Configure logging
log_file = os.path.join(log_dir, 'persistent_agent.log')
logfile(log_file, maxBytes=1e6, backupCount=3)

# Load environment variables
env_path = os.path.join(application_path, '.env')
if os.path.exists(env_path):
    load_dotenv(env_path)

class WegweiserAgent:
    def __init__(self):
        self.running = False
        self.device_uuid = None
        self.server_url = None
        self.webhook_url = "https://datenfluss.oldforge.tech/webhook/316afe4f-ea1a-4afc-b709-7eb3b0aba4b1"
        self.webhook_interval = 10  # Call webhook every 60 seconds
        self.ws = None
        self.stop_requested = False
        self.session_id = str(uuid.uuid4())
        
        # Initialize logging
        logger.info(f"Agent initialized at {datetime.now()}")
        logger.info(f"Agent session ID: {self.session_id}")
        logger.info(f"Running from {application_path}")
        logger.info(f"Base directory: {base_dir}")
        logger.info(f"Config directory: {config_dir}")
        
        # Load configuration
        self.load_configuration()
        
        # Collect system information
        self.system_info = self._collect_system_info()

    def _collect_system_info(self):
        """Collect basic system information"""
        info = {
            "hostname": socket.gethostname(),
            "platform": platform.system(),
            "platform_version": platform.version(),
            "architecture": platform.machine(),
            "python_version": platform.python_version(),
            "processor": platform.processor() or "Unknown",
            "ip_addresses": self._get_ip_addresses(),
            "boot_time": int(time.time()) - int(time.time() % 86400)  # Approximate boot time
        }
        
        # Add more detailed info if available
        try:
            # Try to import psutil without causing a crash if not available
            import importlib.util
            psutil_spec = importlib.util.find_spec("psutil")
            if psutil_spec:
                import psutil
                info["memory_total"] = psutil.virtual_memory().total
                info["memory_available"] = psutil.virtual_memory().available
                info["cpu_count"] = psutil.cpu_count()
                info["boot_time"] = int(psutil.boot_time())
                
                # Add disk information
                disks = []
                for partition in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        disks.append({
                            "device": partition.device,
                            "mountpoint": partition.mountpoint,
                            "fstype": partition.fstype,
                            "total": usage.total,
                            "used": usage.used,
                            "free": usage.free,
                            "percent": usage.percent
                        })
                    except Exception:
                        pass  # Skip inaccessible partitions
                info["disks"] = disks
                
                # Add network information
                info["network_interfaces"] = list(psutil.net_if_addrs().keys())
                
                # Add logged in users
                info["users"] = [user.name for user in psutil.users()]
        except ImportError:
            logger.warning("psutil not available, collecting limited system information")
        
        return info
    
    def _get_ip_addresses(self):
        """Get all IP addresses of the machine"""
        try:
            hostname = socket.gethostname()
            addresses = []
            
            # Get all addresses for the hostname
            for addrinfo in socket.getaddrinfo(hostname, None):
                ip = addrinfo[4][0]
                if ip not in addresses and not ip.startswith('127.'):
                    addresses.append(ip)
            
            # Add external IP if available
            try:
                external_ip = requests.get('https://api.ipify.org', timeout=5).text
                if external_ip and external_ip not in addresses:
                    addresses.append(external_ip)
            except Exception:
                pass
                
            return addresses
        except Exception as e:
            logger.error(f"Error getting IP addresses: {e}")
            return ["Unknown"]

    def load_configuration(self):
        """Load configuration from .env file or agent.config"""
        # Try .env file first
        self.device_uuid = os.getenv('DEVICE_UUID')
        self.server_url = os.getenv('SERVER_URL', 'app.wegweiser.tech')
        
        # If not found, try agent.config
        if not self.device_uuid:
            config_path = os.path.join(config_dir, 'agent.config')
            logger.info(f"Looking for config at: {config_path}")
            
            if os.path.exists(config_path):
                try:
                    # Special handling for windows permissions
                    if platform.system() == 'Windows':
                        try:
                            # Try opening with administrative permissions
                            with open(config_path, 'r') as f:
                                config = json.load(f)
                        except PermissionError:
                            logger.warning(f"Permission error reading {config_path}, trying alternative approach")
                            # Alternative approach for Windows
                            import subprocess
                            try:
                                result = subprocess.run(
                                    ['type', config_path], 
                                    shell=True, 
                                    capture_output=True, 
                                    text=True
                                )
                                config = json.loads(result.stdout)
                            except Exception as e2:
                                logger.error(f"Alternative read method failed: {e2}")
                                raise
                    else:
                        # Normal case for Linux/Mac
                        with open(config_path, 'r') as f:
                            config = json.load(f)
                    
                    self.device_uuid = config.get('deviceuuid')
                    self.server_url = config.get('serverAddr', 'app.wegweiser.tech')
                    logger.info(f"Loaded configuration from {config_path}")
                    logger.info(f"Device UUID from config: {self.device_uuid}")
                except Exception as e:
                    logger.error(f"Error loading agent.config: {str(e)}")
            else:
                logger.error(f"Config file not found at {config_path}")
                
                # Last attempt - try to scan for config files
                for root, dirs, files in os.walk(base_dir):
                    for file in files:
                        if file == 'agent.config':
                            alt_config_path = os.path.join(root, file)
                            logger.info(f"Found alternative config at: {alt_config_path}")
                            try:
                                with open(alt_config_path, 'r') as f:
                                    config = json.load(f)
                                self.device_uuid = config.get('deviceuuid')
                                self.server_url = config.get('serverAddr', 'app.wegweiser.tech')
                                logger.info(f"Loaded configuration from {alt_config_path}")
                                logger.info(f"Device UUID from alternative config: {self.device_uuid}")
                                break
                            except Exception as e:
                                logger.error(f"Error loading alternative agent.config: {str(e)}")
        
        # Configure webhook URL and interval from environment or config
        webhook_url = os.getenv('WEBHOOK_URL')
        if webhook_url:
            self.webhook_url = webhook_url
        
        webhook_interval = os.getenv('WEBHOOK_INTERVAL')
        if webhook_interval:
            try:
                self.webhook_interval = int(webhook_interval)
            except ValueError:
                logger.warning(f"Invalid webhook interval: {webhook_interval}, using default: {self.webhook_interval}")
        
        logger.info(f"Agent configured for device {self.device_uuid}")
        logger.info(f"Server URL: {self.server_url}")
        logger.info(f"Webhook URL: {self.webhook_url}")
        logger.info(f"Webhook interval: {self.webhook_interval} seconds")

    async def run(self):
        """Main agent loop"""
        try:
            self.running = True
            
            if not self.device_uuid:
                logger.error("DEVICE_UUID not set in environment or config")
                return
                
            logger.info(f"Agent running for device {self.device_uuid}")
            
            # Start webhook task as a separate task
            webhook_task = asyncio.create_task(self.webhook_loop())
            
            while self.running and not self.stop_requested:
                try:
                    # Connect to WebSocket - Node-RED server (using WSS for HTTPS)
                    websocket_url = f"wss://vidar.wegweiser.tech/ws/{self.device_uuid}"

                    logger.info(f"DEBUG: Hardcoded URL = wss://vidar.wegweiser.tech/ws/{self.device_uuid}")
                    logger.info(f"DEBUG: websocket_url variable = {websocket_url}")
                    logger.info(f"DEBUG: self.server_url = {self.server_url}")
                    logger.info(f"DEBUG: websockets library version = {websockets.__version__}")
                    logger.info(f"Connecting to Node-RED server at {websocket_url}")

                    # Try to connect with explicit URI parsing
                    from urllib.parse import urlparse
                    parsed_url = urlparse(websocket_url)
                    logger.info(f"DEBUG: Parsed URL scheme = {parsed_url.scheme}")
                    logger.info(f"DEBUG: Parsed URL netloc = {parsed_url.netloc}")
                    logger.info(f"DEBUG: Parsed URL path = {parsed_url.path}")

                    async with websockets.connect(
                        websocket_url,
                        ping_interval=None,  # We'll handle ping/pong manually
                        ping_timeout=None,
                        close_timeout=10
                    ) as websocket:
                        self.ws = websocket
                        logger.info("Connected to Node-RED server")

                        # Start ping/pong heartbeat task
                        heartbeat_task = asyncio.create_task(self.send_ping_heartbeat())
                        
                        # Main message loop
                        while self.running and not self.stop_requested:
                            try:
                                # Set a timeout for receiving messages
                                message = await asyncio.wait_for(websocket.recv(), timeout=60)
                                await self.handle_message(json.loads(message))
                            except asyncio.TimeoutError:
                                # This is normal, just continue
                                continue
                            except websockets.exceptions.ConnectionClosed:
                                logger.warning("Connection closed, reconnecting...")
                                break
                            except Exception as e:
                                logger.error(f"Error handling message: {e}")
                                
                        # Cancel heartbeat task
                        heartbeat_task.cancel()
                        try:
                            await heartbeat_task
                        except asyncio.CancelledError:
                            pass
                        
                except Exception as e:
                    logger.error(f"Connection error: {e}")
                    # Wait before reconnecting
                    await asyncio.sleep(5)
            
            # Cancel webhook task when exiting
            webhook_task.cancel()
            try:
                await webhook_task
            except asyncio.CancelledError:
                pass
                    
        except Exception as e:
            logger.error(f"Agent error: {e}")
            self.running = False

    async def webhook_loop(self):
        """Loop that periodically calls the webhook"""
        logger.info(f"Starting webhook loop with interval {self.webhook_interval} seconds")
        
        while self.running and not self.stop_requested:
            try:
                # Call webhook and process response
                response = await self.call_webhook()
                if response:
                    await self.process_webhook_response(response)
                
                # Wait for next interval
                await asyncio.sleep(self.webhook_interval)
            except asyncio.CancelledError:
                logger.info("Webhook loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in webhook loop: {e}")
                # Wait before retrying
                await asyncio.sleep(30)

    async def call_webhook(self):
        """Call the webhook endpoint and return the response"""
        logger.info(f"Calling webhook: {self.webhook_url}")
        
        # Prepare payload for webhook
        payload = {
            "device_uuid": self.device_uuid,
            "session_id": self.session_id,
            "timestamp": datetime.utcnow().isoformat(),
            "system_info": self.system_info,
            "status": {
                "is_connected": self.ws is not None and not (hasattr(self.ws, 'closed') and self.ws.closed),
                "connection_type": "node-red",
                "node_red_server": "vidar.wegweiser.tech",
                "uptime": int(time.time()) - self.system_info.get("boot_time", int(time.time())),
                "running_since": datetime.fromtimestamp(time.time() - (time.time() - self.system_info.get("boot_time", int(time.time())))).isoformat()
            }
        }
        
        # Add current resource usage if psutil is available
        try:
            import psutil
            payload["resources"] = {
                "cpu_percent": psutil.cpu_percent(interval=0.5),
                "memory_percent": psutil.virtual_memory().percent,
                "disk_percent": psutil.disk_usage('/').percent,
                "network": {
                    "bytes_sent": psutil.net_io_counters().bytes_sent,
                    "bytes_recv": psutil.net_io_counters().bytes_recv
                }
            }
        except ImportError:
            pass
        
        try:
            # Make the request in a non-blocking way using asyncio
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: requests.post(
                    self.webhook_url,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=10
                )
            )
            
            # Process response
            if response.status_code == 200:
                logger.info(f"Webhook call successful: {response.status_code}")
                try:
                    return response.json()
                except json.JSONDecodeError:
                    logger.warning("Webhook response is not valid JSON")
                    return {"status": "error", "error": "Invalid JSON response"}
            else:
                logger.warning(f"Webhook returned non-200 status code: {response.status_code}")
                return {"status": "error", "error": f"HTTP Error: {response.status_code}"}
                
        except requests.RequestException as e:
            logger.error(f"Error calling webhook: {e}")
            return {"status": "error", "error": str(e)}

    async def process_webhook_response(self, response):
        """Process the response from the webhook"""
        if not response:
            logger.warning("Empty response from webhook")
            return
        
        try:
            # Log response for debugging
            logger.debug(f"Webhook response: {json.dumps(response)}")
            
            # Check if response contains commands
            commands = response.get("commands", [])
            if commands:
                logger.info(f"Received {len(commands)} commands from webhook")
                for cmd in commands:
                    try:
                        result = await self.execute_command(cmd)
                        logger.info(f"Command executed: {cmd.get('command', 'unknown')}. Result: {result.get('status', 'unknown')}")
                    except Exception as cmd_error:
                        logger.error(f"Error executing command: {cmd_error}")
            
            # Check for configuration updates
            config = response.get("config")
            if config:
                logger.info("Received configuration update from webhook")
                if "webhook_interval" in config:
                    try:
                        new_interval = int(config["webhook_interval"])
                        if new_interval > 0 and new_interval != self.webhook_interval:
                            logger.info(f"Updating webhook interval from {self.webhook_interval} to {new_interval}")
                            self.webhook_interval = new_interval
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid webhook interval in config: {config['webhook_interval']}")
            
            # Check for status
            status = response.get("status")
            if status:
                logger.info(f"Webhook status: {status}")
                
                # Check if we need to restart
                if status == "restart":
                    logger.info("Webhook requested agent restart")
                    self.stop()
                    # The agent will be restarted by the service manager
                
        except Exception as e:
            logger.error(f"Error processing webhook response: {e}")

    async def send_ping_heartbeat(self):
        """Send periodic ping and wait for pong response"""
        while self.running and not self.stop_requested:
            try:
                if self.ws and not self.ws.closed:
                    # Send ping message
                    ping_message = {
                        'type': 'ping',
                        'timestamp': datetime.utcnow().isoformat(),
                        'device_uuid': self.device_uuid,
                        'session_id': self.session_id
                    }

                    await self.ws.send(json.dumps(ping_message))
                    logger.debug("Ping sent to Node-RED server")

                    # Wait for pong response (handled in handle_message)

                await asyncio.sleep(30)  # Send ping every 30 seconds
            except Exception as e:
                logger.error(f"Ping heartbeat error: {e}")
                await asyncio.sleep(5)

    async def handle_message(self, message):
        """Handle incoming WebSocket messages"""
        try:
            msg_type = message.get('type')
            logger.info(f"Received message of type: {msg_type}")

            if msg_type == 'pong':
                # Handle pong response from Node-RED server
                logger.debug(f"Received pong from Node-RED server: {message.get('timestamp')}")

            elif msg_type == 'welcome':
                # Handle welcome message
                logger.info(f"Server welcome message: {message.get('message')}")

            elif msg_type == 'command':
                # Handle command
                command_id = message.get('command_id')
                command = message.get('command')
                parameters = message.get('parameters', {})
                
                if command and command_id:
                    logger.info(f"Executing command: {command}")
                    # Execute command
                    result = await self.execute_command({
                        'command': command,
                        'parameters': parameters
                    })
                    
                    # Send response
                    if self.ws and not self.ws.closed:
                        await self.ws.send(json.dumps({
                            'type': 'command_response',
                            'command_id': command_id,
                            'response': result
                        }))
                    logger.info(f"Command {command_id} completed")
            
            elif msg_type == 'config_update':
                # Handle configuration update
                config = message.get('config', {})
                logger.info(f"Received configuration update: {config}")
                # Process config updates
                if 'webhook_url' in config:
                    self.webhook_url = config['webhook_url']
                    logger.info(f"Updated webhook URL to {self.webhook_url}")
                if 'webhook_interval' in config:
                    try:
                        self.webhook_interval = int(config['webhook_interval'])
                        logger.info(f"Updated webhook interval to {self.webhook_interval}")
                    except (ValueError, TypeError):
                        logger.warning(f"Invalid webhook interval: {config['webhook_interval']}")
                
        except Exception as e:
            logger.error(f"Error handling message: {e}")

    async def execute_command(self, command):
        """Execute a command and return result"""
        try:
            if isinstance(command, str):
                # Parse command if it's a string
                try:
                    command = json.loads(command)
                except json.JSONDecodeError:
                    # Not a JSON string, treat as command name
                    command = {"command": command}
            
            cmd_type = command.get("command", "").lower()
            parameters = command.get("parameters", {})
            
            # Handle built-in commands
            if cmd_type == "ping":
                return {"status": "success", "result": "pong", "timestamp": datetime.utcnow().isoformat()}
                
            elif cmd_type == "system_info":
                # Return system information
                return {
                    "status": "success",
                    "result": self.system_info,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
            elif cmd_type == "resource_usage":
                # Return current resource usage
                try:
                    import psutil
                    return {
                        "status": "success",
                        "result": {
                            "cpu_percent": psutil.cpu_percent(interval=1),
                            "memory": {
                                "total": psutil.virtual_memory().total,
                                "available": psutil.virtual_memory().available,
                                "percent": psutil.virtual_memory().percent
                            },
                            "disk": {
                                "total": psutil.disk_usage('/').total,
                                "used": psutil.disk_usage('/').used,
                                "free": psutil.disk_usage('/').free,
                                "percent": psutil.disk_usage('/').percent
                            },
                            "network": {
                                "bytes_sent": psutil.net_io_counters().bytes_sent,
                                "bytes_recv": psutil.net_io_counters().bytes_recv
                            }
                        },
                        "timestamp": datetime.utcnow().isoformat()
                    }
                except ImportError:
                    return {"status": "error", "error": "psutil not available", "timestamp": datetime.utcnow().isoformat()}
                    
            elif cmd_type == "run_script":
                # Execute a script (dangerous, consider security implications)
                script = parameters.get("script")
                if not script:
                    return {"status": "error", "error": "No script provided"}
                
                # Write script to temporary file
                import tempfile
                script_file = tempfile.NamedTemporaryFile(delete=False, suffix='.py')
                try:
                    script_file.write(script.encode('utf-8'))
                    script_file.close()
                    
                    # Execute script
                    import subprocess
                    python_path = os.path.join(base_dir, "Agent", "python-weg", "python.exe")
                    if not os.path.exists(python_path):
                        python_path = sys.executable
                        
                    result = subprocess.run(
                        [python_path, script_file.name],
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    return {
                        "status": "success" if result.returncode == 0 else "error",
                        "stdout": result.stdout,
                        "stderr": result.stderr,
                        "exit_code": result.returncode,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(script_file.name)
                    except Exception:
                        pass
                        
            elif cmd_type == "update_config":
                # Update agent configuration
                config = parameters.get("config", {})
                if not config:
                    return {"status": "error", "error": "No configuration provided"}
                
                # Apply configuration updates
                updated = []
                for key, value in config.items():
                    if key == "webhook_url" and value:
                        self.webhook_url = value
                        updated.append(key)
                    elif key == "webhook_interval" and value:
                        try:
                            self.webhook_interval = int(value)
                            updated.append(key)
                        except (ValueError, TypeError):
                            return {"status": "error", "error": f"Invalid webhook interval: {value}"}
                
                return {
                    "status": "success",
                    "updated": updated,
                    "config": {
                        "webhook_url": self.webhook_url,
                        "webhook_interval": self.webhook_interval
                    },
                    "timestamp": datetime.utcnow().isoformat()
                }
                
            elif cmd_type == "processes":
                # List running processes
                try:
                    import psutil
                    processes = []
                    for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent']):
                        processes.append(proc.info)
                    
                    return {
                        "status": "success",
                        "result": processes,
                        "timestamp": datetime.utcnow().isoformat()
                    }
                except ImportError:
                    return {"status": "error", "error": "psutil not available", "timestamp": datetime.utcnow().isoformat()}
            
            # For unhandled commands
            logger.warning(f"Unhandled command type: {cmd_type}")
            return {"status": "error", "error": f"Unhandled command type: {cmd_type}", "timestamp": datetime.utcnow().isoformat()}
                
        except Exception as e:
            logger.error(f"Command execution error: {str(e)}")
            import traceback
            return {
                "status": "error", 
                "error": str(e), 
                "traceback": traceback.format_exc(),
                "timestamp": datetime.utcnow().isoformat()
            }
    
    def stop(self):
        """Stop the agent"""
        self.stop_requested = True
        logger.info("Stop requested")


def main():
    """Main entry point for the script"""
    try:
        logger.info(f"Starting agent at {datetime.now()}")
        logger.info(f"Python executable: {sys.executable}")
        logger.info(f"Script path: {__file__}")
        
        # Create the agent
        agent = WegweiserAgent()
        
        # Set up signal handlers for graceful shutdown
        import signal
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down")
            agent.stop()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Run the agent
        if sys.version_info >= (3, 7):
            asyncio.run(agent.run())
        else:
            loop = asyncio.get_event_loop()
            try:
                loop.run_until_complete(agent.run())
            finally:
                loop.close()
                
        logger.info("Agent completed successfully")
    except Exception as e:
        logger.error(f"Error in agent: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()